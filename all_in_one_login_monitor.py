#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
整合版客服登录监控工具
此文件整合了以下模块的功能：
- config.py - 配置信息
- qiyu_api.py - 七鱼API调用
- isolated_browser.py - 浏览器控制
- browser_control.py - 浏览器控制包装
- perform_login_sequence.py - 登录序列
- login_monitor_gui.py - GUI界面

所有功能都整合到一个文件中，无需调用其他本地模块。
"""

import os
import sys
import time
import json
import hashlib
import logging
import threading
import traceback
import queue
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import requests
import multiprocessing
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# =====================================================================
# 配置部分 (原 config.py)
# =====================================================================

# 七鱼API配置
QIYU_APP_KEY = "3b9d81fd743a018bb30158d99043adc5"  # 替换为实际的APP KEY
QIYU_APP_SECRET = "3E27394508A14673894EAB5636D22BFF"  # 替换为实际的APP SECRET
QIYU_STAFF_ID = "6481806"  # 替换为要监控的客服ID

# API URLs
QIYU_BASE_URL = "https://qiyukf.com"
QIYU_STAFF_ONLINE_URL = f"{QIYU_BASE_URL}/openapi/data/overview/staff/list/online"

# 监控设置
CHECK_INTERVAL_SECONDS = 60  # 每分钟检查一次
BROWSER_OPERATION_TIMEOUT = 15  # 浏览器操作超时时间（秒）

# 浏览器设置
BROWSER_OPEN_URL = "https://giantnetwork.qiyukf.com/chat/"

# 延迟设置
DELAY_AFTER_OPEN_URL = 5  # 打开URL后等待的秒数
DELAY_AFTER_SUBMIT = 5  # 点击提交按钮后等待的秒数

# GUI配置
GUI_CONFIG_FILE = "gui_config.json"
DEFAULT_GUI_CONFIG = {
    "staff_id": "6481806",
    "check_interval": 60,
    "username": "wangluqing",
    "password": "ztgame@123"
}

# 日志设置
LOG_FOLDER = "logs"
os.makedirs(LOG_FOLDER, exist_ok=True)

# =====================================================================
# 浏览器隔离进程部分 (原 isolated_browser.py)
# =====================================================================

class BrowserProcess:
    """在独立进程中管理浏览器操作的类"""

    def __init__(self, timeout=30):
        """初始化浏览器进程管理器"""
        self.timeout = timeout
        self.process = None
        self.task_queue = multiprocessing.Queue()
        self.result_queue = multiprocessing.Queue()

    def start(self):
        """如果浏览器进程未运行，则启动它"""
        if self.process is None or not self.process.is_alive():
            logging.info("启动浏览器进程...")
            self.task_queue = multiprocessing.Queue()
            self.result_queue = multiprocessing.Queue()
            self.process = multiprocessing.Process(
                target=self._browser_process_main,
                args=(self.task_queue, self.result_queue, self.timeout)
            )
            self.process.daemon = True  # 主程序退出时进程将终止
            self.process.start()
            logging.info(f"浏览器进程已启动，PID: {self.process.pid}")
            return True
        return False

    def stop(self):
        """停止浏览器进程"""
        if self.process and self.process.is_alive():
            logging.info("停止浏览器进程...")
            try:
                # 发送终止命令
                self.task_queue.put(("terminate", None))
                # 等待进程终止
                self.process.join(5)
                # 如果进程仍在运行，强制终止
                if self.process.is_alive():
                    logging.warning("浏览器进程未响应终止命令，强制终止...")
                    self.process.terminate()
                    self.process.join(2)
                logging.info("浏览器进程已停止")
                return True
            except Exception as e:
                logging.error(f"停止浏览器进程时出错: {str(e)}")
                return False
        return True  # 如果进程已经不在运行，则视为成功

    def restart(self):
        """重启浏览器进程"""
        logging.info("重启浏览器进程...")
        self.stop()
        time.sleep(1)  # 等待资源释放
        return self.start()

    def is_running(self):
        """检查浏览器进程是否正在运行"""
        return self.process is not None and self.process.is_alive()

    def execute_task(self, task_name, task_args=None, timeout=None):
        """在浏览器进程中执行任务"""
        if not self.process or not self.process.is_alive():
            logging.warning("浏览器进程未运行，正在启动...")
            if not self.start():
                return False, "启动浏览器进程失败"

        if timeout is None:
            timeout = self.timeout

        try:
            # 将任务放入队列
            logging.info(f"向浏览器进程发送任务 '{task_name}'")
            self.task_queue.put((task_name, task_args))

            # 等待结果，带超时
            start_time = time.time()
            while time.time() - start_time < timeout:
                if not self.result_queue.empty():
                    success, result = self.result_queue.get()
                    logging.info(f"任务 '{task_name}' 完成，成功={success}")
                    return success, result

                # 检查进程是否仍在运行
                if not self.process.is_alive():
                    logging.error("浏览器进程意外终止，正在重启...")
                    self.restart()
                    return False, "任务执行期间浏览器进程终止"

                time.sleep(0.1)

            # 如果到这里，任务超时
            logging.error(f"任务 '{task_name}' 在 {timeout} 秒后超时")
            # 尝试重启浏览器进程
            self.restart()
            return False, f"任务在 {timeout} 秒后超时"

        except Exception as e:
            logging.error(f"执行任务 '{task_name}' 时出错: {str(e)}")
            # 尝试重启浏览器进程
            self.restart()
            return False, f"错误: {str(e)}"

    @staticmethod
    def _browser_process_main(task_queue, result_queue, default_timeout):
        """浏览器进程的主函数"""
        logging.info("浏览器进程已启动")
        driver = None

        try:
            # 初始化浏览器
            driver = BrowserProcess._initialize_browser()
            logging.info("浏览器已初始化")

            # 处理任务直到终止
            while True:
                try:
                    # 从队列获取任务，带超时
                    task_name, task_args = task_queue.get(timeout=0.5)

                    # 检查是否应该终止
                    if task_name == "terminate":
                        logging.info("收到终止命令")
                        break

                    # 执行任务
                    logging.info(f"执行任务: {task_name}")
                    if task_name == "open_url":
                        success, result = BrowserProcess._task_open_url(driver, task_args)
                    elif task_name == "execute_script":
                        success, result = BrowserProcess._task_execute_script(driver, task_args)
                    elif task_name == "perform_login_sequence":
                        success, result = BrowserProcess._task_perform_login_sequence(driver, task_args)
                    else:
                        success, result = False, f"未知任务: {task_name}"

                    # 发送结果
                    result_queue.put((success, result))

                except queue.Empty:
                    # 队列为空，继续等待
                    pass
                except Exception as e:
                    logging.error(f"处理任务时出错: {str(e)}")
                    result_queue.put((False, f"处理任务时出错: {str(e)}"))

        except Exception as e:
            logging.error(f"浏览器进程主循环出错: {str(e)}")
            traceback.print_exc()
        finally:
            # 清理资源
            if driver:
                try:
                    driver.quit()
                    logging.info("浏览器已关闭")
                except:
                    logging.error("关闭浏览器时出错")
            logging.info("浏览器进程已终止")

    @staticmethod
    def _initialize_browser():
        """初始化Chrome浏览器"""
        logging.info("初始化Chrome浏览器...")
        chrome_options = Options()
        # 取消下面一行的注释可以在无头模式下运行Chrome
        # chrome_options.add_argument("--headless")
        chrome_options.add_argument("--start-maximized")

        # 添加稳定性选项
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-infobars")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--ignore-certificate-errors")

        try:
            # 创建Chrome WebDriver
            driver = webdriver.Chrome(options=chrome_options)
            return driver
        except Exception as e:
            logging.error(f"初始化浏览器时出错: {str(e)}")
            raise

    @staticmethod
    def _task_open_url(driver, args):
        """打开URL的任务"""
        try:
            url = args.get("url", BROWSER_OPEN_URL)
            logging.info(f"打开URL: {url}")
            driver.get(url)
            time.sleep(DELAY_AFTER_OPEN_URL)  # 等待页面加载
            return True, "URL已成功打开"
        except Exception as e:
            logging.error(f"打开URL时出错: {str(e)}")
            return False, f"打开URL时出错: {str(e)}"

    @staticmethod
    def _task_execute_script(driver, args):
        """执行JavaScript脚本的任务"""
        try:
            script = args.get("script", "")
            logging.info(f"执行脚本: {script[:50]}...")
            result = driver.execute_script(script)
            return True, result
        except Exception as e:
            logging.error(f"执行脚本时出错: {str(e)}")
            return False, f"执行脚本时出错: {str(e)}"

    @staticmethod
    def _task_perform_login_sequence(driver, args):
        """执行完整登录序列的任务"""
        try:
            # 从参数获取用户名和密码，如果没有提供则使用默认值
            username = args.get('username', 'wangluqing')
            password = args.get('password', 'ztgame@123')

            logging.info(f"开始登录序列，使用用户名: {username}")

            # 1. 打开七鱼聊天URL
            logging.info(f"打开登录页面: {BROWSER_OPEN_URL}")
            driver.get(BROWSER_OPEN_URL)
            logging.info(f"页面已打开，等待{DELAY_AFTER_OPEN_URL}秒...")
            time.sleep(DELAY_AFTER_OPEN_URL)  # 等待页面加载

            # 额外等待2秒再输入用户名和密码
            logging.info("额外等待2秒再输入用户名和密码...")
            time.sleep(2)

            # 3. 使用JavaScript输入用户名和密码
            logging.info(f"使用JavaScript输入用户名'{username}'和密码...")
            try:
                # 定义一个函数来模拟用户输入
                driver.execute_script(f"""
                function simulateInput(element, value) {{
                    element.focus();
                    element.value = value;
                    element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                    element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                }}

                // 输入用户名
                const usernameInput = document.querySelector('input[name="username"]') || document.getElementById('username');
                if (usernameInput) simulateInput(usernameInput, "{username}");

                // 输入密码
                const passwordInput = document.querySelector('input[name="password"]') || document.getElementById('password');
                if (passwordInput) simulateInput(passwordInput, "{password}");

                return true;
                """)
                logging.info("用户名和密码输入成功")
            except Exception as e:
                logging.error(f"输入用户名和密码失败: {str(e)}")
                return False, f"输入用户名和密码失败: {str(e)}"

            # 输入用户名和密码后等待2秒再点击登录按钮
            logging.info("输入完成后等待2秒再点击登录按钮...")
            time.sleep(2)

            # 4. 点击提交按钮
            logging.info("点击登录按钮")
            try:
                # 使用多种方法尝试点击登录按钮
                # 首先尝试使用Selenium直接点击
                try:
                    logging.info("尝试使用Selenium直接点击登录按钮...")
                    submit_button = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, "//button[@type='submit']"))
                    )
                    submit_button.click()
                    logging.info("使用Selenium成功点击登录按钮")
                    success = True
                except Exception as e:
                    logging.warning(f"使用Selenium点击登录按钮失败: {str(e)}，尝试使用JavaScript...")
                    success = False

                # 如果Selenium方法失败，尝试使用JavaScript
                if not success:
                    # 使用多种选择器尝试查找登录按钮
                    result = driver.execute_script("""
                    // 尝试多种可能的选择器
                    const submitButton = document.querySelector('button[type="submit"]') ||
                                        document.querySelector('input[type="submit"]') ||
                                        document.querySelector('button.submit') ||
                                        document.querySelector('button.login') ||
                                        document.querySelector('button:contains("登录")') ||
                                        document.querySelector('button:contains("Login")');

                    if (submitButton) {
                        submitButton.click();
                        return true;
                    }

                    // 如果上面的选择器都没找到，尝试查找所有按钮并点击可能的登录按钮
                    const allButtons = document.querySelectorAll('button');
                    for (let btn of allButtons) {
                        if (btn.textContent.includes('登录') ||
                            btn.textContent.includes('Login') ||
                            btn.textContent.toLowerCase().includes('login') ||
                            btn.className.includes('login') ||
                            btn.className.includes('submit')) {
                            btn.click();
                            return true;
                        }
                    }

                    return false;
                    """)

                    if result:
                        logging.info("使用JavaScript成功点击登录按钮")
                    else:
                        # 最后尝试使用Enter键提交表单
                        logging.warning("JavaScript未找到登录按钮，尝试使用Enter键提交...")
                        try:
                            # 尝试在密码输入框上按Enter键
                            password_input = driver.find_element(By.ID, "password")
                            password_input.send_keys(Keys.RETURN)
                            logging.info("使用Enter键提交表单")
                            result = True
                        except Exception as e:
                            logging.error(f"使用Enter键提交失败: {str(e)}")
                            result = False

                if not result and not success:
                    logging.error("所有方法都未能点击登录按钮")
                    return False, "未找到登录按钮"
            except Exception as e:
                logging.error(f"点击登录按钮失败: {str(e)}")
                return False, f"点击登录按钮失败: {str(e)}"

            # 5. 等待操作完成
            logging.info(f"登录按钮已点击，等待{DELAY_AFTER_SUBMIT}秒...")
            time.sleep(DELAY_AFTER_SUBMIT)

            # 5.5 关闭任何弹出对话框（如果存在）
            try:
                logging.info("检查是否有弹出对话框...")
                # 使用JavaScript检查并关闭弹出对话框
                result = driver.execute_script("""
                const closeButton = document.querySelector('button.close') ||
                                   document.querySelector('button.cancel') ||
                                   document.querySelector('.dialog .close');
                if (closeButton) {
                    closeButton.click();
                    return true;
                }
                return false;
                """)

                if result:
                    logging.info("找到弹出对话框并关闭")
                    logging.info("弹出对话框已关闭，等待5秒...")
                    time.sleep(5)  # 关闭弹出窗口后等待
                else:
                    logging.info("没有找到弹出对话框，继续...")
            except Exception as e:
                logging.warning(f"处理弹出对话框时出错: {str(e)}")
                # 继续执行，因为这可能不是关键错误

            # 额外等待5秒再点击在线状态按钮
            logging.info("额外等待5秒再点击在线状态按钮...")
            time.sleep(5)

            # 6. 点击在线状态按钮
            logging.info("尝试点击在线状态按钮...")
            try:
                # 使用JavaScript点击在线状态按钮
                result = driver.execute_script("""
                // 尝试多种可能的选择器
                const onlineButton = document.querySelector('.item:has(> .icon.online)') ||
                                    document.querySelector('button.online') ||
                                    document.querySelector('button.status') ||
                                    document.querySelector('[class*="online"]') ||
                                    document.querySelector('[class*="status"]');
                if (onlineButton) {
                    onlineButton.click();
                    return true;
                }
                return false;
                """)

                if result:
                    logging.info("在线状态按钮点击成功")
                else:
                    logging.warning("未找到在线状态按钮，可能已经在线")
            except Exception as e:
                logging.warning(f"点击在线状态按钮时出错: {str(e)}")
                # 继续执行，因为这可能不是关键错误

            logging.info("登录序列成功完成")
            return True, "登录序列成功完成"

        except Exception as e:
            logging.error(f"登录序列期间出错: {str(e)}")
            return False, f"登录序列期间出错: {str(e)}"

# 创建浏览器进程管理器的全局实例
browser_process = BrowserProcess()

# =====================================================================
# 浏览器控制包装部分 (原 browser_control.py)
# =====================================================================

def is_browser_running():
    """检查浏览器进程是否正在运行"""
    return browser_process.is_running()

def restart_browser():
    """重启浏览器进程"""
    logging.info("重启浏览器进程...")
    return browser_process.restart()

def open_url(url, timeout=BROWSER_OPERATION_TIMEOUT):
    """
    使用Selenium在浏览器中打开URL

    参数:
        url (str): 要打开的URL
        timeout (int, optional): 超时时间（秒）。默认为BROWSER_OPERATION_TIMEOUT

    返回:
        bool: 如果URL成功打开则为True，否则为False
    """
    logging.info(f"打开URL: {url}")
    result = browser_process.execute_task("open_url", {"url": url}, timeout)[0]
    if result:
        logging.info("URL成功打开")
    else:
        logging.error("打开URL时出错")
    return result

def perform_login_sequence(username=None, password=None, timeout=60):
    """
    在隔离进程中使用Selenium执行完整的登录序列:
    1. 打开七鱼聊天URL
    2. 等待页面加载5秒
    3. 查找用户名输入框
    4. 额外等待2秒
    5. 使用JavaScript输入用户名和密码
    6. 使用JavaScript点击提交按钮
    7. 等待操作完成
    8. 如果存在弹出对话框，关闭它
    9. 关闭弹出窗口后等待5秒
    10. 额外等待5秒
    11. 点击在线状态按钮

    参数:
        username (str, optional): 登录用户名。如果为None，则使用默认值
        password (str, optional): 登录密码。如果为None，则使用默认值
        timeout (int, optional): 超时时间（秒）。默认为60

    返回:
        bool: 如果所有步骤都成功完成则为True，否则为False
    """
    logging.info("在隔离进程中开始登录序列...")
    if username is not None:
        logging.info(f"使用自定义用户名: {username}")
    if password is not None:
        logging.info("使用自定义密码")

    result = browser_process.execute_task("perform_login_sequence",
                                         {"username": username, "password": password},
                                         timeout)[0]
    if result:
        logging.info("登录序列成功完成")
    else:
        logging.error("登录序列失败")
    return result

def close_browser():
    """关闭浏览器进程"""
    logging.info("关闭浏览器进程...")
    result = browser_process.stop()
    if result:
        logging.info("浏览器进程成功关闭")
    else:
        logging.error("关闭浏览器进程时出错")
    return result

# =====================================================================
# 七鱼API部分 (原 qiyu_api.py)
# =====================================================================

def calculate_md5(content):
    """计算内容的MD5哈希值"""
    if isinstance(content, str):
        content = content.encode('utf-8')
    return hashlib.md5(content).hexdigest().lower()

def calculate_checksum(app_secret, md5_content, timestamp):
    """计算七鱼API认证的校验和"""
    content = f"{app_secret}{md5_content}{timestamp}"
    return hashlib.sha1(content.encode('utf-8')).hexdigest().lower()

def get_staff_online_status(group_ids=None):
    """
    获取客服成员的在线状态

    参数:
        group_ids (list, optional): 按组ID过滤客服的列表。默认为None

    返回:
        dict: 包含客服在线状态信息的API响应
    """
    try:
        # 准备请求参数
        timestamp = str(int(time.time()))

        # 准备请求体
        request_body = {}
        if group_ids:
            request_body["groupIds"] = group_ids

        # 计算请求体的MD5
        request_body_json = json.dumps(request_body)
        md5_content = calculate_md5(request_body_json)

        # 计算校验和
        checksum = calculate_checksum(QIYU_APP_SECRET, md5_content, timestamp)

        # 准备带查询参数的URL
        url = f"{QIYU_STAFF_ONLINE_URL}?appKey={QIYU_APP_KEY}&time={timestamp}&checksum={checksum}"

        # 发起API请求
        logging.info(f"向七鱼API发起请求: {url}")
        response = requests.post(
            url,
            data=request_body_json,
            headers={"Content-Type": "application/json;charset=utf-8"}
        )

        # 解析并返回响应
        response_data = response.json()
        logging.info(f"收到七鱼API响应: {response_data}")

        if response_data.get("code") != 200:
            logging.error(f"七鱼API错误: {response_data}")
            return None

        return response_data

    except Exception as e:
        logging.error(f"获取客服在线状态时出错: {str(e)}")
        return None

def is_staff_online(staff_id, group_ids=None):
    """
    检查特定客服成员是否在线

    参数:
        staff_id (str): 要检查的客服成员ID
        group_ids (list, optional): 按组ID过滤的列表。默认为None

    返回:
        bool: 如果客服在线（状态=1）则为True，否则为False
    """
    response = get_staff_online_status(group_ids)

    if not response or "list" not in response:
        logging.error("获取客服在线状态失败或响应格式无效")
        return False

    # 在响应中查找客服成员
    for staff in response["list"]:
        if str(staff.get("staffId")) == str(staff_id):
            status = staff.get("status")
            logging.info(f"客服 {staff_id} 状态: {status}")
            return status == 1

    logging.warning(f"在响应中未找到客服 {staff_id}")
    return False

# =====================================================================
# 登录序列部分 (原 perform_login_sequence.py)
# =====================================================================

def perform_login_with_retry(max_retries=3, username="wangluqing", password="ztgame@123"):
    """
    使用重试机制执行完整的登录序列
    这是browser_control.perform_login_sequence函数的包装器，
    添加了重试功能

    参数:
        max_retries (int): 如果操作失败，最大重试次数
        username (str): 登录用户名
        password (str): 登录密码

    返回:
        bool: 如果所有步骤都成功完成则为True，否则为False
    """
    logging.info(f"开始登录序列，最大重试次数 {max_retries}")

    for attempt in range(max_retries):
        logging.info(f"登录尝试 {attempt+1}/{max_retries}")

        try:
            # 使用browser_control模块执行登录序列
            success = perform_login_sequence(username=username, password=password)

            if success:
                logging.info("登录序列成功完成")
                return True
            else:
                logging.error("登录序列失败")

                if attempt < max_retries - 1:
                    logging.info(f"等待5秒后重试 {attempt+2}...")
                    time.sleep(5)
                else:
                    logging.error("所有重试尝试都失败")
                    return False

        except Exception as e:
            logging.error(f"登录尝试 {attempt+1} 期间出错: {str(e)}")

            # 如果出错，关闭浏览器
            try:
                close_browser()
            except Exception as close_error:
                logging.error(f"关闭浏览器时出错: {str(close_error)}")

            if attempt < max_retries - 1:
                logging.info(f"等待5秒后重试 {attempt+2}...")
                time.sleep(5)
            else:
                logging.error("所有重试尝试都失败")
                return False

    return False

# =====================================================================
# GUI部分 (原 login_monitor_gui.py)
# =====================================================================

# 配置日志记录以与GUI一起工作
class QueueHandler(logging.Handler):
    """
    将日志记录发送到队列的类。
    可以从不同的线程使用。
    """
    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue

    def emit(self, record):
        self.log_queue.put(record)

class LoginMonitorGUI:
    """登录监控应用程序的主GUI类"""

    def __init__(self, root):
        """初始化GUI"""
        try:
            # 设置主窗口
            self.root = root
            self.root.title("客服登录监控工具")
            self.root.geometry("800x600")
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

            # 变量
            self.running = False
            self.monitor_thread = None
            self.staff_id = tk.StringVar(value="6481806")  # 默认值
            self.check_interval = tk.IntVar(value=60)  # 默认值（秒）
            self.username = tk.StringVar(value="wangluqing")  # 默认用户名
            self.password = tk.StringVar(value="ztgame@123")  # 默认密码

            # 如果存在则加载配置
            self.config_file = GUI_CONFIG_FILE
            self.load_config()

            # 设置日志记录
            self.log_queue = queue.Queue()
            self.setup_logging()

            # 创建GUI元素
            self.create_widgets()

            # 开始轮询日志消息队列
            self.poll_log_queue()

        except Exception as e:
            print(f"初始化GUI时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def setup_logging(self):
        """设置日志记录以将消息发送到GUI"""
        try:
            # 创建队列处理程序
            queue_handler = QueueHandler(self.log_queue)
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            queue_handler.setFormatter(formatter)
            queue_handler.setLevel(logging.INFO)

            # 获取根日志记录器并添加队列处理程序
            root_logger = logging.getLogger()
            root_logger.addHandler(queue_handler)

            # 确保根日志记录器的级别足够低
            if root_logger.level > logging.INFO:
                root_logger.setLevel(logging.INFO)

            logging.info("GUI日志记录已设置")
        except Exception as e:
            print(f"设置日志记录时出错: {str(e)}")

    def create_widgets(self):
        """创建GUI小部件"""
        try:
            # 创建主框架
            main_frame = ttk.Frame(self.root, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 创建配置框架
            config_frame = ttk.LabelFrame(main_frame, text="配置", padding="10")
            config_frame.pack(fill=tk.X, pady=5)

            # 客服ID
            ttk.Label(config_frame, text="客服ID:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Entry(config_frame, textvariable=self.staff_id, width=20).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

            # 检查间隔
            ttk.Label(config_frame, text="检查间隔（秒）:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
            ttk.Entry(config_frame, textvariable=self.check_interval, width=10).grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)

            # 用户名
            ttk.Label(config_frame, text="用户名:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Entry(config_frame, textvariable=self.username, width=20).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

            # 密码
            ttk.Label(config_frame, text="密码:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
            password_entry = ttk.Entry(config_frame, textvariable=self.password, width=20, show="*")
            password_entry.grid(row=1, column=3, sticky=tk.W, padx=5, pady=5)

            # 按钮框架
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=5)

            # 启动按钮
            self.start_button = ttk.Button(button_frame, text="启动监控", command=self.start_monitoring)
            self.start_button.pack(side=tk.LEFT, padx=5)

            # 停止按钮
            self.stop_button = ttk.Button(button_frame, text="停止监控", command=self.stop_monitoring, state=tk.DISABLED)
            self.stop_button.pack(side=tk.LEFT, padx=5)

            # 保存配置按钮
            self.save_config_button = ttk.Button(button_frame, text="保存配置", command=self.save_config)
            self.save_config_button.pack(side=tk.LEFT, padx=5)

            # 手动登录按钮
            self.manual_login_button = ttk.Button(button_frame, text="手动登录", command=self.manual_login)
            self.manual_login_button.pack(side=tk.LEFT, padx=5)

            # 日志框架
            log_frame = ttk.LabelFrame(main_frame, text="日志", padding="10")
            log_frame.pack(fill=tk.BOTH, expand=True, pady=5)

            # 日志文本区域
            self.log_text = scrolledtext.ScrolledText(log_frame, height=20)
            self.log_text.pack(fill=tk.BOTH, expand=True)
            self.log_text.config(state=tk.DISABLED)  # 使其只读

            # 状态栏
            self.status_var = tk.StringVar(value="就绪")
            status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
            status_bar.pack(side=tk.BOTTOM, fill=tk.X)

            logging.info("GUI小部件已创建")
        except Exception as e:
            logging.error(f"创建小部件时出错: {str(e)}")

    def poll_log_queue(self):
        """轮询日志队列并更新日志文本小部件"""
        try:
            # 检查队列中是否有新的日志记录
            while not self.log_queue.empty():
                record = self.log_queue.get(block=False)
                self.display_log(record)
        except Exception as e:
            print(f"轮询日志队列时出错: {str(e)}")
        finally:
            # 安排下一次轮询
            self.root.after(100, self.poll_log_queue)

    def display_log(self, record):
        """在日志文本小部件中显示日志记录"""
        msg = self.format_log_record(record)
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, msg + '\n')
        self.log_text.see(tk.END)  # 自动滚动到底部
        self.log_text.config(state=tk.DISABLED)

    def format_log_record(self, record):
        """格式化日志记录以供显示"""
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        return formatter.format(record)

    def load_config(self):
        """如果存在则从文件加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.staff_id.set(config.get('staff_id', '6481806'))
                    self.check_interval.set(config.get('check_interval', 60))
                    self.username.set(config.get('username', 'wangluqing'))
                    self.password.set(config.get('password', 'ztgame@123'))
                    logging.info(f"配置已从 {self.config_file} 加载")
        except Exception as e:
            logging.error(f"加载配置时出错: {str(e)}")

    def save_config(self):
        """将当前配置保存到文件"""
        try:
            config = {
                'staff_id': self.staff_id.get().strip(),
                'check_interval': self.check_interval.get(),
                'username': self.username.get().strip(),
                'password': self.password.get().strip()
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            logging.info(f"配置已保存到 {self.config_file}")
            messagebox.showinfo("保存配置", "配置已成功保存")
        except Exception as e:
            logging.error(f"保存配置时出错: {str(e)}")
            messagebox.showerror("错误", f"保存配置时出错: {str(e)}")

    def start_monitoring(self):
        """启动监控线程"""
        if not self.running:
            try:
                # 获取配置值
                staff_id = self.staff_id.get().strip()
                check_interval = self.check_interval.get()

                if not staff_id:
                    messagebox.showerror("错误", "请输入有效的客服ID")
                    return

                if check_interval < 10:
                    messagebox.showerror("错误", "检查间隔必须至少为10秒")
                    return

                # 更新状态
                self.running = True
                self.status_var.set("监控中...")
                self.start_button.config(state=tk.DISABLED)
                self.stop_button.config(state=tk.NORMAL)

                # 启动监控线程
                self.monitor_thread = threading.Thread(
                    target=self.monitor_loop,
                    args=(staff_id, check_interval),
                    daemon=True
                )
                self.monitor_thread.start()

                logging.info(f"已启动监控客服ID {staff_id}，间隔 {check_interval} 秒")
            except Exception as e:
                self.running = False
                logging.error(f"启动监控时出错: {str(e)}")
                messagebox.showerror("错误", f"启动监控时出错: {str(e)}")
                self.status_var.set("错误")
                self.start_button.config(state=tk.NORMAL)
                self.stop_button.config(state=tk.DISABLED)

    def stop_monitoring(self):
        """停止监控线程"""
        if self.running:
            self.running = False
            self.status_var.set("已停止")
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            logging.info("监控已停止")

    def manual_login(self):
        """手动触发登录序列"""
        try:
            username = self.username.get().strip()
            password = self.password.get().strip()

            if not username or not password:
                messagebox.showerror("错误", "请输入有效的用户名和密码")
                return

            # 禁用按钮
            self.manual_login_button.config(state=tk.DISABLED)
            self.status_var.set("正在执行登录...")

            # 在单独的线程中执行登录
            threading.Thread(
                target=self.perform_manual_login,
                args=(username, password),
                daemon=True
            ).start()

        except Exception as e:
            logging.error(f"手动登录时出错: {str(e)}")
            messagebox.showerror("错误", f"手动登录时出错: {str(e)}")
            self.manual_login_button.config(state=tk.NORMAL)
            self.status_var.set("错误")

    def perform_manual_login(self, username, password):
        """在单独的线程中执行手动登录"""
        try:
            logging.info("开始手动登录序列...")

            # 首先尝试关闭任何现有的浏览器进程
            logging.info("检查浏览器进程是否正在运行...")
            if is_browser_running():
                logging.info("发现现有浏览器进程，正在关闭...")
                close_browser()
            else:
                logging.info("未发现现有浏览器进程，跳过关闭操作")

            # 执行登录序列
            success = perform_login_with_retry(username=username, password=password)

            if success:
                logging.info("手动登录序列成功完成")
                self.root.after(0, lambda: messagebox.showinfo("成功", "登录序列成功完成"))
            else:
                logging.error("手动登录序列失败")
                self.root.after(0, lambda: messagebox.showerror("错误", "登录序列失败"))

        except Exception as e:
            logging.error(f"手动登录期间出错: {str(e)}")
            self.root.after(0, lambda: messagebox.showerror("错误", f"登录期间出错: {str(e)}"))
        finally:
            # 重新启用按钮
            self.root.after(0, lambda: self.manual_login_button.config(state=tk.NORMAL))
            self.root.after(0, lambda: self.status_var.set("就绪"))

    def monitor_loop(self, staff_id, check_interval):
        """监控循环，检查客服状态并在需要时触发登录"""
        last_action_time = 0

        while self.running:
            try:
                logging.info(f"检查客服 {staff_id} 的状态...")

                # 检查客服是否在线
                is_online = is_staff_online(staff_id)

                if is_online:
                    logging.info(f"客服 {staff_id} 在线")
                else:
                    logging.info(f"客服 {staff_id} 不在线. 触发登录序列...")

                    # 首先尝试关闭任何现有的浏览器进程
                    logging.info("检查浏览器进程是否正在运行...")
                    if is_browser_running():
                        logging.info("发现现有浏览器进程，正在关闭...")
                        close_browser()
                    else:
                        logging.info("未发现现有浏览器进程，跳过关闭操作")

                    # 执行登录序列
                    try:
                        # 从GUI获取用户名和密码
                        username = self.username.get().strip()
                        password = self.password.get().strip()

                        logging.info("使用配置的登录凭证进行登录...")

                        # 使用配置的凭证执行登录序列
                        success = perform_login_with_retry(username=username, password=password)

                        if success:
                            logging.info("登录序列成功完成")
                            last_action_time = time.time()
                        else:
                            logging.error("登录序列失败")
                    except Exception as e:
                        logging.error(f"登录序列期间出错: {str(e)}")

                # 等待指定的间隔时间
                for _ in range(check_interval):
                    if not self.running:
                        break
                    time.sleep(1)

            except Exception as e:
                logging.error(f"监控循环期间出错: {str(e)}")
                time.sleep(10)  # 出错后等待一段时间再重试

    def on_closing(self):
        """窗口关闭时的处理程序"""
        if messagebox.askokcancel("退出", "确定要退出吗?"):
            self.running = False
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(1)
            # 关闭浏览器
            try:
                if is_browser_running():
                    close_browser()
            except:
                pass
            self.root.destroy()

# =====================================================================
# 主函数
# =====================================================================

def main():
    """启动GUI应用程序的主函数"""
    try:
        # 配置基本的控制台日志记录
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler()
            ]
        )
        logger = logging.getLogger("main")
        logger.info("启动登录监控GUI应用程序...")

        # 创建根窗口
        logger.info("创建Tkinter根窗口...")
        root = tk.Tk()

        # 创建应用程序
        logger.info("初始化LoginMonitorGUI...")
        app = LoginMonitorGUI(root)

        # 启动主循环
        logger.info("启动Tkinter主循环...")
        root.mainloop()

    except Exception as e:
        print(f"主函数中出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()