#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
在线状态切换功能测试程序
用于单独测试和调试在线状态切换功能
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_online_status.log', encoding='utf-8')
    ]
)

def initialize_browser():
    """初始化Chrome浏览器"""
    logging.info("初始化Chrome浏览器...")
    chrome_options = Options()
    # 取消下面一行的注释可以在无头模式下运行Chrome
    # chrome_options.add_argument("--headless")
    chrome_options.add_argument("--start-maximized")

    # 添加稳定性选项
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-infobars")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--ignore-certificate-errors")

    try:
        # 创建Chrome WebDriver
        driver = webdriver.Chrome(options=chrome_options)
        logging.info("浏览器初始化成功")
        return driver
    except Exception as e:
        logging.error(f"初始化浏览器时出错: {str(e)}")
        raise

def test_selenium_xpath_method(driver):
    """测试Selenium XPath方法"""
    logging.info("=== 测试Selenium XPath方法 ===")
    try:
        # 使用XPath直接查找在线状态span元素
        online_span = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//div[@id='sessionStatus-1']/span[normalize-space()='在线']"))
        )
        logging.info("通过XPath找到了'在线'span元素")
        
        # 点击该span元素
        online_span.click()
        logging.info("成功点击了'在线'span元素")
        return True
    except TimeoutException:
        logging.warning("Selenium XPath方法：等待元素超时")
        return False
    except Exception as e:
        logging.warning(f"Selenium XPath方法失败: {str(e)}")
        return False

def test_selenium_alternative_method(driver):
    """测试Selenium备选方法"""
    logging.info("=== 测试Selenium备选方法 ===")
    try:
        # 首先尝试找到ID为sessionStatus-1的div元素
        parent_div = WebDriverWait(driver, 5).until(
            EC.presence_of_element_located((By.ID, "sessionStatus-1"))
        )
        logging.info("找到了ID为sessionStatus-1的div元素")
        
        # 然后在该div中查找包含"在线"文本的span元素
        online_span = parent_div.find_element(By.XPATH, ".//span[text()='在线']")
        logging.info("找到了包含'在线'文本的span元素")
        
        # 点击该span元素
        online_span.click()
        logging.info("成功点击了'在线'span元素")
        return True
    except Exception as e:
        logging.warning(f"Selenium备选方法失败: {str(e)}")
        return False

def test_javascript_xpath_method(driver):
    """测试JavaScript XPath方法"""
    logging.info("=== 测试JavaScript XPath方法 ===")
    try:
        result = driver.execute_script("""
        // 使用XPath方法
        var xpathExpression = "//div[@id='sessionStatus-1']/span[normalize-space()='在线']";
        var result = document.evaluate(xpathExpression, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
        var spanToClick = result.singleNodeValue;
        
        if (spanToClick) {
          console.log('通过 XPath 找到了 "在线" span 元素');
          spanToClick.click();
          console.log('通过 XPath 已点击 "在线" 按钮。');
          return true;
        } else {
          console.log('通过 XPath 未找到 "在线" span 元素');
          return false;
        }
        """)
        
        if result:
            logging.info("JavaScript XPath方法成功")
            return True
        else:
            logging.warning("JavaScript XPath方法：未找到元素")
            return False
    except Exception as e:
        logging.warning(f"JavaScript XPath方法失败: {str(e)}")
        return False

def test_javascript_alternative_method(driver):
    """测试JavaScript备选方法"""
    logging.info("=== 测试JavaScript备选方法 ===")
    try:
        result = driver.execute_script("""
        // 通过ID查找方法
        var parentDiv = document.getElementById('sessionStatus-1');
        var targetSpan = null;
        
        if (parentDiv) {
          console.log('找到了ID为sessionStatus-1的div元素');
          var spansInDiv = parentDiv.getElementsByTagName('span');
          for (var i = 0; i < spansInDiv.length; i++) {
            if (spansInDiv[i].textContent.trim() === '在线') {
              targetSpan = spansInDiv[i];
              console.log('找到了包含"在线"文本的span元素');
              break;
            }
          }
        }
        
        if (targetSpan) {
          console.log('点击"在线"span元素');
          targetSpan.click();
          return true;
        } else {
          console.log('未找到"在线"span元素');
          return false;
        }
        """)
        
        if result:
            logging.info("JavaScript备选方法成功")
            return True
        else:
            logging.warning("JavaScript备选方法：未找到元素")
            return False
    except Exception as e:
        logging.warning(f"JavaScript备选方法失败: {str(e)}")
        return False

def check_page_elements(driver):
    """检查页面元素状态"""
    logging.info("=== 检查页面元素状态 ===")
    try:
        # 检查页面是否加载完成
        ready_state = driver.execute_script("return document.readyState")
        logging.info(f"页面加载状态: {ready_state}")
        
        # 检查是否存在sessionStatus-1元素
        session_status_exists = driver.execute_script("""
        var element = document.getElementById('sessionStatus-1');
        if (element) {
            console.log('找到sessionStatus-1元素');
            console.log('元素HTML:', element.outerHTML);
            return {
                exists: true,
                html: element.outerHTML,
                textContent: element.textContent
            };
        } else {
            console.log('未找到sessionStatus-1元素');
            return {exists: false};
        }
        """)
        
        if session_status_exists['exists']:
            logging.info("找到sessionStatus-1元素")
            logging.info(f"元素内容: {session_status_exists['textContent']}")
            logging.info(f"元素HTML: {session_status_exists['html']}")
        else:
            logging.warning("未找到sessionStatus-1元素")
            
        # 检查所有包含"在线"的元素
        online_elements = driver.execute_script("""
        var elements = document.querySelectorAll('*');
        var onlineElements = [];
        for (var i = 0; i < elements.length; i++) {
            if (elements[i].textContent && elements[i].textContent.includes('在线')) {
                onlineElements.push({
                    tagName: elements[i].tagName,
                    id: elements[i].id,
                    className: elements[i].className,
                    textContent: elements[i].textContent.trim(),
                    outerHTML: elements[i].outerHTML
                });
            }
        }
        return onlineElements;
        """)
        
        logging.info(f"找到 {len(online_elements)} 个包含'在线'的元素:")
        for i, element in enumerate(online_elements):
            logging.info(f"元素 {i+1}: {element['tagName']} - ID: {element['id']} - Class: {element['className']} - Text: {element['textContent']}")
            
    except Exception as e:
        logging.error(f"检查页面元素时出错: {str(e)}")

def main():
    """主测试函数"""
    driver = None
    try:
        # 初始化浏览器
        driver = initialize_browser()
        
        # 打开登录页面
        url = "https://giantnetwork.qiyukf.com/chat/"
        logging.info(f"打开页面: {url}")
        driver.get(url)
        
        # 等待页面加载
        logging.info("等待页面加载...")
        time.sleep(10)  # 给足够的时间让页面完全加载
        
        # 检查页面元素状态
        check_page_elements(driver)
        
        # 等待用户手动登录
        input("请手动完成登录，然后按Enter键继续测试在线状态切换功能...")
        
        # 再次检查页面元素状态
        logging.info("登录后检查页面元素状态...")
        check_page_elements(driver)
        
        # 测试各种方法
        methods = [
            ("Selenium XPath方法", test_selenium_xpath_method),
            ("JavaScript XPath方法", test_javascript_xpath_method),
            ("Selenium备选方法", test_selenium_alternative_method),
            ("JavaScript备选方法", test_javascript_alternative_method)
        ]
        
        success_count = 0
        for method_name, method_func in methods:
            logging.info(f"\n开始测试: {method_name}")
            try:
                if method_func(driver):
                    logging.info(f"✓ {method_name} 成功")
                    success_count += 1
                    time.sleep(2)  # 等待2秒再测试下一个方法
                else:
                    logging.warning(f"✗ {method_name} 失败")
            except Exception as e:
                logging.error(f"✗ {method_name} 出错: {str(e)}")
            
            time.sleep(1)  # 方法之间的间隔
        
        logging.info(f"\n测试完成，成功方法数: {success_count}/{len(methods)}")
        
        # 保持浏览器打开以便观察
        input("测试完成，按Enter键关闭浏览器...")
        
    except Exception as e:
        logging.error(f"测试过程中出错: {str(e)}")
    finally:
        if driver:
            try:
                driver.quit()
                logging.info("浏览器已关闭")
            except:
                logging.error("关闭浏览器时出错")

if __name__ == "__main__":
    main()
