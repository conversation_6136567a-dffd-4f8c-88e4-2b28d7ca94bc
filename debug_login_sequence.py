#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
登录序列调试程序
用于详细调试登录序列的每个步骤
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# 配置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('debug_login_sequence.log', encoding='utf-8')
    ]
)

def initialize_browser():
    """初始化Chrome浏览器"""
    logging.info("初始化Chrome浏览器...")
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-infobars")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--ignore-certificate-errors")

    try:
        driver = webdriver.Chrome(options=chrome_options)
        logging.info("浏览器初始化成功")
        return driver
    except Exception as e:
        logging.error(f"初始化浏览器时出错: {str(e)}")
        raise

def debug_login_sequence(driver, username="wangluqing", password="ztgame@123"):
    """调试版登录序列"""
    try:
        logging.info(f"=== 开始登录序列调试，用户名: {username} ===")
        
        # 步骤1: 打开登录页面
        url = "https://giantnetwork.qiyukf.com/chat/"
        logging.info(f"步骤1: 打开登录页面 {url}")
        driver.get(url)
        logging.info("页面已打开，等待5秒...")
        time.sleep(5)
        
        # 检查页面标题和URL
        current_url = driver.current_url
        page_title = driver.title
        logging.info(f"当前URL: {current_url}")
        logging.info(f"页面标题: {page_title}")
        
        # 步骤2: 额外等待2秒
        logging.info("步骤2: 额外等待2秒...")
        time.sleep(2)
        
        # 步骤3: 检查页面元素
        logging.info("步骤3: 检查页面元素...")
        page_source_snippet = driver.page_source[:500] + "..." if len(driver.page_source) > 500 else driver.page_source
        logging.debug(f"页面源码片段: {page_source_snippet}")
        
        # 查找用户名输入框
        username_found = False
        username_input = None
        try:
            username_input = driver.find_element(By.ID, "username")
            username_found = True
            logging.info("✓ 找到用户名输入框 (ID: username)")
        except:
            try:
                username_input = driver.find_element(By.NAME, "username")
                username_found = True
                logging.info("✓ 找到用户名输入框 (NAME: username)")
            except:
                logging.error("✗ 未找到用户名输入框")
                
                # 查找所有input元素
                all_inputs = driver.find_elements(By.TAG_NAME, "input")
                logging.info(f"页面上找到 {len(all_inputs)} 个input元素:")
                for i, inp in enumerate(all_inputs):
                    inp_type = inp.get_attribute("type")
                    inp_id = inp.get_attribute("id")
                    inp_name = inp.get_attribute("name")
                    inp_class = inp.get_attribute("class")
                    logging.info(f"  Input {i+1}: type={inp_type}, id={inp_id}, name={inp_name}, class={inp_class}")
        
        if not username_found:
            logging.error("无法找到用户名输入框，登录序列失败")
            return False
            
        # 步骤4: 输入用户名和密码
        logging.info("步骤4: 输入用户名和密码...")
        try:
            # 使用JavaScript输入
            result = driver.execute_script(f"""
            function simulateInput(element, value) {{
                element.focus();
                element.value = value;
                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
            }}

            // 输入用户名
            const usernameInput = document.querySelector('input[name="username"]') || document.getElementById('username');
            if (usernameInput) {{
                console.log('找到用户名输入框，输入用户名');
                simulateInput(usernameInput, "{username}");
            }} else {{
                console.log('未找到用户名输入框');
                return false;
            }}

            // 输入密码
            const passwordInput = document.querySelector('input[name="password"]') || document.getElementById('password');
            if (passwordInput) {{
                console.log('找到密码输入框，输入密码');
                simulateInput(passwordInput, "{password}");
            }} else {{
                console.log('未找到密码输入框');
                return false;
            }}

            return true;
            """)
            
            if result:
                logging.info("✓ 用户名和密码输入成功")
            else:
                logging.error("✗ 用户名和密码输入失败")
                return False
                
        except Exception as e:
            logging.error(f"输入用户名和密码时出错: {str(e)}")
            return False
        
        # 步骤5: 等待2秒后点击登录按钮
        logging.info("步骤5: 等待2秒后点击登录按钮...")
        time.sleep(2)
        
        # 查找登录按钮
        login_button_found = False
        try:
            # 首先尝试使用Selenium
            try:
                submit_button = driver.find_element(By.CSS_SELECTOR, "span.j-submitBtn")
                submit_button.click()
                login_button_found = True
                logging.info("✓ 使用Selenium成功点击登录按钮 (span.j-submitBtn)")
            except:
                try:
                    submit_button = driver.find_element(By.XPATH, "//button[@type='submit']")
                    submit_button.click()
                    login_button_found = True
                    logging.info("✓ 使用Selenium成功点击登录按钮 (button[type='submit'])")
                except:
                    logging.warning("Selenium方法失败，尝试JavaScript...")
                    
            # 如果Selenium失败，尝试JavaScript
            if not login_button_found:
                result = driver.execute_script("""
                // 查找span.j-submitBtn
                const submitSpan = document.querySelector('span.j-submitBtn');
                if (submitSpan) {
                    console.log('找到span.j-submitBtn，点击它');
                    submitSpan.click();
                    return true;
                }
                
                // 查找button[type="submit"]
                const submitButton = document.querySelector('button[type="submit"]');
                if (submitButton) {
                    console.log('找到button[type="submit"]，点击它');
                    submitButton.click();
                    return true;
                }
                
                // 查找所有包含"登录"文本的元素
                const allElements = document.querySelectorAll('*');
                for (let el of allElements) {
                    if (el.textContent && el.textContent.trim() === '登录') {
                        console.log('找到包含"登录"文本的元素，点击它');
                        el.click();
                        return true;
                    }
                }
                
                console.log('未找到登录按钮');
                return false;
                """)
                
                if result:
                    login_button_found = True
                    logging.info("✓ 使用JavaScript成功点击登录按钮")
                else:
                    logging.error("✗ 所有方法都未能找到登录按钮")
                    
                    # 列出所有可能的按钮
                    all_buttons = driver.find_elements(By.TAG_NAME, "button")
                    all_spans = driver.find_elements(By.TAG_NAME, "span")
                    logging.info(f"页面上找到 {len(all_buttons)} 个button元素:")
                    for i, btn in enumerate(all_buttons):
                        btn_text = btn.text
                        btn_id = btn.get_attribute("id")
                        btn_class = btn.get_attribute("class")
                        logging.info(f"  Button {i+1}: text='{btn_text}', id={btn_id}, class={btn_class}")
                    
                    logging.info(f"页面上找到 {len(all_spans)} 个span元素:")
                    for i, span in enumerate(all_spans):
                        span_text = span.text
                        span_id = span.get_attribute("id")
                        span_class = span.get_attribute("class")
                        if span_text or 'submit' in (span_class or '').lower() or 'login' in (span_class or '').lower():
                            logging.info(f"  Span {i+1}: text='{span_text}', id={span_id}, class={span_class}")
                    
                    return False
                    
        except Exception as e:
            logging.error(f"点击登录按钮时出错: {str(e)}")
            return False
        
        # 步骤6: 等待登录完成
        logging.info("步骤6: 等待登录完成...")
        time.sleep(5)
        
        # 检查登录后的页面
        current_url_after = driver.current_url
        page_title_after = driver.title
        logging.info(f"登录后URL: {current_url_after}")
        logging.info(f"登录后页面标题: {page_title_after}")
        
        # 步骤7: 查找在线状态按钮
        logging.info("步骤7: 查找在线状态按钮...")
        online_button_found = False
        
        try:
            # 首先检查sessionStatus-1元素是否存在
            session_status_info = driver.execute_script("""
            var element = document.getElementById('sessionStatus-1');
            if (element) {
                return {
                    exists: true,
                    html: element.outerHTML,
                    text: element.textContent
                };
            } else {
                return {exists: false};
            }
            """)
            
            if session_status_info['exists']:
                logging.info("✓ 找到sessionStatus-1元素")
                logging.info(f"元素内容: {session_status_info['text']}")
                
                # 尝试点击在线状态
                result = driver.execute_script("""
                var xpathExpression = "//div[@id='sessionStatus-1']/span[normalize-space()='在线']";
                var result = document.evaluate(xpathExpression, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
                var spanToClick = result.singleNodeValue;
                
                if (spanToClick) {
                    console.log('通过XPath找到在线状态span，点击它');
                    spanToClick.click();
                    return true;
                } else {
                    console.log('通过XPath未找到在线状态span');
                    return false;
                }
                """)
                
                if result:
                    online_button_found = True
                    logging.info("✓ 成功点击在线状态按钮")
                else:
                    logging.warning("✗ 未找到在线状态span元素")
            else:
                logging.warning("✗ 未找到sessionStatus-1元素")
                
                # 查找所有包含"在线"的元素
                online_elements = driver.execute_script("""
                var elements = document.querySelectorAll('*');
                var found = [];
                for (var i = 0; i < elements.length; i++) {
                    if (elements[i].textContent && elements[i].textContent.includes('在线')) {
                        found.push({
                            tag: elements[i].tagName,
                            id: elements[i].id,
                            class: elements[i].className,
                            text: elements[i].textContent.trim()
                        });
                    }
                }
                return found;
                """)
                
                logging.info(f"找到 {len(online_elements)} 个包含'在线'的元素:")
                for i, elem in enumerate(online_elements):
                    logging.info(f"  {i+1}. {elem['tag']} - ID: {elem['id']} - Class: {elem['class']} - Text: {elem['text']}")
                    
        except Exception as e:
            logging.error(f"查找在线状态按钮时出错: {str(e)}")
        
        # 总结
        if login_button_found:
            logging.info("✓ 登录序列基本完成（登录按钮已点击）")
            if online_button_found:
                logging.info("✓ 在线状态切换也成功完成")
                return True
            else:
                logging.warning("⚠ 登录完成但在线状态切换可能失败")
                return True  # 登录本身成功
        else:
            logging.error("✗ 登录序列失败（未能点击登录按钮）")
            return False
            
    except Exception as e:
        logging.error(f"登录序列期间出错: {str(e)}")
        import traceback
        logging.error(f"详细错误信息: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    driver = None
    try:
        # 初始化浏览器
        driver = initialize_browser()
        
        # 执行调试版登录序列
        success = debug_login_sequence(driver)
        
        if success:
            print("\n=== 登录序列调试完成：成功 ===")
        else:
            print("\n=== 登录序列调试完成：失败 ===")
            
        input("按Enter键关闭浏览器...")
        
    except Exception as e:
        logging.error(f"主函数出错: {str(e)}")
    finally:
        if driver:
            driver.quit()
            logging.info("浏览器已关闭")

if __name__ == "__main__":
    main()
